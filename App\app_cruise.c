// Copyright (c) 2024 米醋电子工作室
// 文件名: app_cruise.c
// 描述: 巡航模式实现文件，实现云台自动巡航扫描功能

#include "app_cruise.h"
#include "app_pid.h"
#include "app_mode.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include <math.h>

// 定义PI常数（如果math.h中没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 巡航状态变量
static cruise_state_t cruise_state = CRUISE_IDLE;
static cruise_params_t cruise_params;
static MultiTimer mt_cruise;

// 螺旋扫描变量
static float current_angle = 0.0f;      // 当前角度
static float current_radius = 0.0f;     // 当前半径
static int target_x = 0;                // 当前目标X坐标
static int target_y = 0;                // 当前目标Y坐标
static bool is_moving = false;          // 是否正在移动

// 状态名称数组
static const char* cruise_state_names[] = {
    "IDLE",         // 空闲
    "SCANNING",     // 扫描中
    "FOUND_TARGET", // 发现目标
    "TRACKING"      // 追踪中
};

/**
 * @brief 巡航初始化
 */
void app_cruise_init(void)
{
    // 初始化默认参数
    cruise_params.center_x = CRUISE_CENTER_X;
    cruise_params.center_y = CRUISE_CENTER_Y;
    cruise_params.radius_max = CRUISE_RADIUS_MAX;
    cruise_params.step = CRUISE_STEP;
    cruise_params.speed = CRUISE_SPEED;
    cruise_params.dwell_time = CRUISE_DWELL_TIME;
    cruise_params.mode = SCAN_SPIRAL;
    
    // 初始化状态
    cruise_state = CRUISE_IDLE;
    current_angle = 0.0f;
    current_radius = 0.0f;
    is_moving = false;
    
    my_printf(&huart1, "Cruise Mode Initialized\r\n");
}

/**
 * @brief 计算螺旋扫描的下一个位置
 */
static void calculate_next_spiral_position(void)
{
    // 螺旋扫描算法：角度递增，半径逐渐增大
    current_angle += 0.5f; // 角度步进
    
    // 当完成一圈时，增加半径
    if (current_angle >= 2 * M_PI) {
        current_angle = 0.0f;
        current_radius += cruise_params.step;
    }
    
    // 检查是否超出最大半径
    if (current_radius > cruise_params.radius_max) {
        // 重新开始螺旋扫描
        current_radius = 0.0f;
        current_angle = 0.0f;
        my_printf(&huart1, "Cruise: Restart spiral scan\r\n");
    }
    
    // 计算目标坐标
    target_x = cruise_params.center_x + (int)(current_radius * cos(current_angle));
    target_y = cruise_params.center_y + (int)(current_radius * sin(current_angle));
    
    // 边界检查
    if (target_x < 50) target_x = 50;
    if (target_x > 590) target_x = 590;
    if (target_y < 50) target_y = 50;
    if (target_y > 430) target_y = 430;
}

/**
 * @brief 开始巡航
 */
void app_cruise_start(void)
{
    if (cruise_state != CRUISE_IDLE) {
        return; // 已经在巡航中
    }
    
    my_printf(&huart1, "Start Cruise Mode\r\n");
    
    // 设置巡航状态
    cruise_state = CRUISE_SCANNING;
    
    // 重置扫描参数
    current_angle = 0.0f;
    current_radius = 0.0f;
    is_moving = false;
    
    // 设置MaixCam回调为巡航回调
    maixcam_set_callback(app_cruise_callback);
    
    // 初始化PID（但不启动）
    app_pid_init();
    
    // 启动巡航任务定时器
    multiTimerStart(&mt_cruise, cruise_params.speed, app_cruise_task, NULL);
}

/**
 * @brief 停止巡航
 */
void app_cruise_stop(void)
{
    if (cruise_state == CRUISE_IDLE) {
        return; // 已经停止
    }
    
    my_printf(&huart1, "Stop Cruise Mode\r\n");
    
    // 停止巡航任务定时器
    multiTimerStop(&mt_cruise);
    
    // 停止PID控制
    app_pid_stop();
    
    // 恢复默认MaixCam回调
    maixcam_set_callback(NULL);
    
    // 重置状态
    cruise_state = CRUISE_IDLE;
    is_moving = false;
}

/**
 * @brief 巡航任务函数
 */
void app_cruise_task(MultiTimer *timer, void *userData)
{
    if (cruise_state != CRUISE_SCANNING) {
        return; // 不在扫描状态
    }
    
    if (!is_moving) {
        // 计算下一个扫描位置
        calculate_next_spiral_position();
        
        // 设置PID目标位置
        app_pid_set_target(target_x, target_y);
        
        // 标记为移动中
        is_moving = true;
        
        my_printf(&huart1, "Cruise: Move to (%d,%d)\r\n", target_x, target_y);
        
        // 设置停留时间定时器
        multiTimerStart(&mt_cruise, cruise_params.dwell_time, app_cruise_task, NULL);
    } else {
        // 停留时间结束，准备移动到下一个位置
        is_moving = false;
        
        // 继续巡航
        multiTimerStart(&mt_cruise, cruise_params.speed, app_cruise_task, NULL);
    }
}

/**
 * @brief 巡航模式的MaixCam回调函数
 */
void app_cruise_callback(LaserCoord_t coord)
{
    if (coord.type == RED_LASER_ID) // 发现目标
    {
        my_printf(&huart1, "Cruise: Target found at (%d,%d)!\r\n", coord.x, coord.y);
        
        // 更新状态为发现目标
        cruise_state = CRUISE_FOUND_TARGET;
        
        // 停止巡航定时器
        multiTimerStop(&mt_cruise);
        
        // 设置目标位置
        app_pid_set_target(coord.x, coord.y);
        
        // 启动PID追踪
        app_pid_start();
        
        // 切换到追踪状态
        cruise_state = CRUISE_TRACKING;
        
        // 通知模式管理器切换到追踪模式
        // 这里可以通过回调或事件通知上层切换模式
        my_printf(&huart1, "Cruise: Auto switch to tracking mode\r\n");
    }
    else if (coord.type == GREEN_LASER_ID) // 实际激光点坐标
    {
        // 在巡航模式下，绿色激光点用于位置反馈
        // 可以用来验证云台是否到达目标位置
    }
}

/**
 * @brief 获取巡航状态
 */
cruise_state_t app_cruise_get_state(void)
{
    return cruise_state;
}

/**
 * @brief 设置巡航参数
 */
void app_cruise_set_params(cruise_params_t params)
{
    cruise_params = params;
    my_printf(&huart1, "Cruise: Parameters updated\r\n");
}

/**
 * @brief 获取状态名称
 */
const char* app_cruise_get_state_name(cruise_state_t state)
{
    if (state < sizeof(cruise_state_names) / sizeof(cruise_state_names[0])) {
        return cruise_state_names[state];
    }
    return "UNKNOWN";
}
