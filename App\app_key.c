// Copyright (c) 2024 米醋电子工作室
// 文件名: app_key.c
// 描述: 按键处理实现文件，提供PA0按键单击检测功能

#include "app_key.h"
#include "mydefine.h"

// 按键状态变量
static uint8_t key_val = 0;  // 当前按键值
static uint8_t key_old = 0;  // 上次按键值
static uint8_t key_down = 0; // 按键按下事件
static uint8_t key_up = 0;   // 按键释放事件

/**
 * @brief 读取按键状态
 * @return 按键值：1-按键按下，0-按键释放
 */
uint8_t key_read(void)
{
    // 读取PA0按键状态，按下为低电平(GPIO_PIN_RESET)
    if(HAL_GPIO_ReadPin(key_GPIO_Port, key_Pin) == GPIO_PIN_RESET)
        return KEY_PRESSED;
    else
        return KEY_RELEASED;
}

/**
 * @brief 按键处理函数
 * @note 需要定时调用此函数进行按键扫描和状态更新
 */
void key_proc(void)
{
    key_val = key_read();                           // 读取当前按键状态
    key_down = key_val & (key_val ^ key_old);       // 检测按键按下事件
    key_up = ~key_val & (key_val ^ key_old);        // 检测按键释放事件
    key_old = key_val;                              // 保存当前状态为下次的历史状态
    
    // 处理按键按下事件
    if(key_down == KEY_PRESSED)
    {
        // 在此处添加按键按下时的处理逻辑
        // 例如：切换LED状态、发送命令等

        // 示例：通过串口输出按键按下信息（需要包含相关头文件）
        // my_printf(&huart1, "Key pressed!\r\n");

        // 示例：切换激光器状态（需要包含app.h）
        // static uint8_t laser_state = 0;
        // if(laser_state) {
        //     app_laser_off();
        //     laser_state = 0;
        // } else {
        //     app_laser_on();
        //     laser_state = 1;
        // }
    }
}

/**
 * @brief 获取按键按下事件
 * @return 1-有按键按下事件，0-无按键按下事件
 */
uint8_t get_key_down(void)
{
    return key_down;
}

/**
 * @brief 获取按键释放事件
 * @return 1-有按键释放事件，0-无按键释放事件
 */
uint8_t get_key_up(void)
{
    return key_up;
}

/**
 * @brief 按键任务函数
 * @param timer 定时器指针
 * @param userData 用户数据指针
 * @note 定时调用此函数进行按键扫描
 */
void key_task(MultiTimer *timer, void *userData)
{
    key_proc(); // 执行按键处理

    // 重新启动按键扫描定时器
    multiTimerStart(&mt_key, KEY_TASK_TIME, key_task, NULL);
}
