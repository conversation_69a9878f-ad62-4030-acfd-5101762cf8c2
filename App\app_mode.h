// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.h
// 描述: 系统模式管理头文件，定义不同工作模式和状态机

#ifndef __APP_MODE_H
#define __APP_MODE_H

#include "main.h"

// 系统工作模式枚举
typedef enum {
    MODE_IDLE = 0,      // 默认状态：只使能，不追踪
    MODE_TRACKING,      // 模式一：追踪模式
    MODE_CUSTOM,        // 模式二：自定义模式（预留）
    MODE_MAX            // 模式数量（用于循环切换）
} system_mode_t;

// 函数声明
void app_mode_init(void);                    // 模式管理初始化
void app_mode_switch(void);                  // 模式切换
system_mode_t app_mode_get_current(void);    // 获取当前模式
const char* app_mode_get_name(system_mode_t mode); // 获取模式名称

#endif /* __APP_MODE_H */
