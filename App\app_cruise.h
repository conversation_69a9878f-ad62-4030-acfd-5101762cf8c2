// Copyright (c) 2024 米醋电子工作室
// 文件名: app_cruise.h
// 描述: 巡航模式头文件，实现云台自动巡航扫描功能

#ifndef __APP_CRUISE_H
#define __APP_CRUISE_H

#include "main.h"
#include "MultiTimer.h"
#include "app_maixcam.h"

// 巡航扫描参数配置
#define CRUISE_CENTER_X     320     // 扫描中心X坐标
#define CRUISE_CENTER_Y     240     // 扫描中心Y坐标
#define CRUISE_RADIUS_MAX   200     // 最大扫描半径
#define CRUISE_STEP         15      // 扫描步长
#define CRUISE_SPEED        50      // 巡航速度(ms)
#define CRUISE_DWELL_TIME   100     // 每个位置停留时间(ms)

// 巡航状态枚举
typedef enum {
    CRUISE_IDLE = 0,        // 巡航空闲
    CRUISE_SCANNING,        // 正在扫描
    CRUISE_FOUND_TARGET,    // 发现目标
    CRUISE_TRACKING         // 切换到追踪
} cruise_state_t;

// 扫描模式枚举
typedef enum {
    SCAN_SPIRAL = 0,        // 螺旋扫描
    SCAN_GRID,              // 栅格扫描
    SCAN_RANDOM             // 随机扫描
} scan_mode_t;

// 巡航参数结构体
typedef struct {
    int center_x;           // 扫描中心X
    int center_y;           // 扫描中心Y
    int radius_max;         // 最大扫描半径
    int step;               // 扫描步长
    int speed;              // 巡航速度
    int dwell_time;         // 停留时间
    scan_mode_t mode;       // 扫描模式
} cruise_params_t;

// 函数声明
void app_cruise_init(void);                         // 巡航初始化
void app_cruise_start(void);                        // 开始巡航
void app_cruise_stop(void);                         // 停止巡航
void app_cruise_task(MultiTimer *timer, void *userData); // 巡航任务函数
void app_cruise_callback(LaserCoord_t coord);       // 巡航回调函数
cruise_state_t app_cruise_get_state(void);          // 获取巡航状态
void app_cruise_set_params(cruise_params_t params); // 设置巡航参数
const char* app_cruise_get_state_name(cruise_state_t state); // 获取状态名称

#endif /* __APP_CRUISE_H */
