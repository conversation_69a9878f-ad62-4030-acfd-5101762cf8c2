// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.c
// 描述: 系统模式管理实现文件，处理不同工作模式的切换和管理

#include "app_mode.h"
#include "app_pid.h"
#include "app_uasrt.h"

// 当前系统模式
static system_mode_t current_mode = MODE_IDLE;

// 模式名称数组
static const char* mode_names[] = {
    "IDLE",      // 空闲模式
    "TRACKING",  // 追踪模式
    "CUSTOM"     // 自定义模式
};

/**
 * @brief 模式管理初始化
 */
void app_mode_init(void)
{
    current_mode = MODE_IDLE; // 上电默认为空闲模式
    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 模式切换函数
 */
void app_mode_switch(void)
{
    // 退出当前模式
    switch(current_mode)
    {
        case MODE_IDLE:
            // 空闲模式无需特殊退出处理
            break;
            
        case MODE_TRACKING:
            // 退出追踪模式：停止PID控制
            app_pid_stop();
            my_printf(&huart1, "Exit Tracking Mode\r\n");
            break;
            
        case MODE_CUSTOM:
            // 模式二退出处理（预留）
            my_printf(&huart1, "Exit Custom Mode\r\n");
            break;
            
        default:
            break;
    }
    
    // 切换到下一个模式
    current_mode = (current_mode + 1) % MODE_MAX;
    
    // 进入新模式
    switch(current_mode)
    {
        case MODE_IDLE:
            my_printf(&huart1, "Enter Idle Mode\r\n");
            break;
            
        case MODE_TRACKING:
            // 进入追踪模式：启动PID控制
            my_printf(&huart1, "Enter Tracking Mode\r\n");
            app_pid_init();
            app_pid_start();
            break;
            
        case MODE_CUSTOM:
            // 模式二进入处理（预留）
            my_printf(&huart1, "Enter Custom Mode (Not Implemented)\r\n");
            break;
            
        default:
            break;
    }
    
    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 获取当前模式
 * @return 当前系统模式
 */
system_mode_t app_mode_get_current(void)
{
    return current_mode;
}

/**
 * @brief 获取模式名称
 * @param mode 模式枚举值
 * @return 模式名称字符串
 */
const char* app_mode_get_name(system_mode_t mode)
{
    if(mode < MODE_MAX)
        return mode_names[mode];
    else
        return "UNKNOWN";
}
